import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { createPortal } from 'react-dom';
import { FiX } from 'react-icons/fi';
import Boton from './Boton';

const GlobalSettings = ({ isOpen, onClose, pageSettings, onUpdatePageSettings, currentPageName }) => {
  const [settings, setSettings] = useState({
    enableDarkness: true,
    ...pageSettings
  });

  useEffect(() => {
    setSettings({
      enableDarkness: true,
      ...pageSettings
    });
  }, [pageSettings]);

  const handleSave = () => {
    onUpdatePageSettings(settings);
    onClose();
  };

  if (!isOpen) return null;

  const content = (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-xl w-96 max-w-[90vw]">
        <div className="flex justify-between items-center bg-gray-700 px-4 py-3 rounded-t-lg">
          <h2 className="font-bold text-white">Ajustes de Página</h2>
          <button 
            onClick={onClose} 
            className="text-gray-400 hover:text-red-400 transition-colors"
          >
            <FiX size={20} />
          </button>
        </div>
        
        <div className="p-4 space-y-4">
          <div className="text-sm text-gray-300 mb-3">
            Configurando: <span className="font-semibold text-white">{currentPageName}</span>
          </div>
          
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="pageEnableDarkness"
              checked={settings.enableDarkness}
              onChange={e => setSettings({ ...settings, enableDarkness: e.target.checked })}
              className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="pageEnableDarkness" className="text-white text-sm">
              Activar sistema de oscuridad en esta página
            </label>
          </div>
          
          <div className="text-xs text-gray-400 mt-2">
            💡 Este ajuste solo afecta a la página actual. La iluminación de tokens funciona independientemente de esta configuración.
          </div>
          
          <div className="text-xs text-yellow-400 mt-2">
            ⚠️ Al desactivar la oscuridad, se revelará todo el mapa pero los tokens con luz seguirán iluminando normalmente.
          </div>
        </div>
        
        <div className="flex justify-end gap-2 p-4 bg-gray-750 rounded-b-lg">
          <Boton color="gray" onClick={onClose}>
            Cancelar
          </Boton>
          <Boton color="green" onClick={handleSave}>
            Guardar
          </Boton>
        </div>
      </div>
    </div>
  );

  return createPortal(content, document.body);
};

GlobalSettings.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  pageSettings: PropTypes.object.isRequired,
  onUpdatePageSettings: PropTypes.func.isRequired,
  currentPageName: PropTypes.string.isRequired,
};

export default GlobalSettings;